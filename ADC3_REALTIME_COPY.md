# ADC3实时复制功能说明

## 功能概述
第八个按钮现在实现了ADC3实时复制功能，具有以下特性：

1. **FFT基频检测**：每4096个采样点执行一次FFT分析，仅用于确定输入信号的基频
2. **实时信号复制**：DAC实时输出与ADC3输入信号相同波形和幅度的信号
3. **连续采样**：ADC3以815534Hz的采样率连续采样，不间断

## 按钮功能
- **按钮文本**：`COPY OFF` / `COPY ON`
- **按钮位置**：第八个按钮（右下角）
- **功能切换**：点击按钮在实时复制模式和关闭状态之间切换

## 工作原理

### 实时复制模式启动时：
1. 设置`adc3_realtime_mode = 1`标志
2. 启动ADC3连续采样（815534Hz采样率）
3. 设置DAC为实时模式，停止正弦波DMA输出
4. 启用DAC用户模式

### ADC3中断处理：
1. 每次ADC3转换完成时触发中断
2. 读取ADC转换结果
3. **实时输出**：如果是实时模式，立即将ADC值输出到DAC
4. **数据存储**：同时将数据存储到缓冲区用于FFT分析

### 基频检测：
1. 每收集4096个采样点后执行FFT分析
2. 寻找频谱中的最大峰值确定基频
3. 在LCD上显示检测到的基频
4. 重置采样索引，继续下一轮4096点采样

### 实时复制模式停止时：
1. 清除`adc3_realtime_mode = 0`标志
2. 停止ADC3采样
3. 停止DAC实时模式
4. 禁用DAC用户模式

## 技术细节

### 采样频率
- **ADC3采样率**：815534Hz（由TIM3触发）
- **实时延迟**：约1.2μs（一个采样周期）

### 信号路径
```
输入信号 → ADC3(PF7) → 中断处理 → DAC(PA4) → 输出信号
                    ↓
                 数据缓冲区 → FFT分析 → 基频检测
```

### 内存使用
- **ADC3缓冲区**：4096 × 2字节 = 8KB
- **FFT缓冲区**：共享现有的fft_inputbuf和fft_outputbuf

## 使用方法

1. **启动实时复制**：
   - 按KEY0选择第八个按钮（COPY OFF）
   - 按KEY1激活，按钮变为绿色（COPY ON）
   - LCD显示"ADC3: Real-time copy mode"

2. **连接信号**：
   - 将输入信号连接到PF7引脚（ADC3输入）
   - 从PA4引脚（DAC输出）获取复制的信号

3. **观察结果**：
   - 输出信号应与输入信号具有相同的波形和幅度
   - LCD显示检测到的基频，如"Detected: 1000.0Hz"

4. **停止实时复制**：
   - 再次按KEY1，按钮变为灰色（COPY OFF）
   - LCD显示"ADC3: Copy mode stopped"

## 注意事项

1. **输入电压范围**：0V - 3.3V（ADC输入范围）
2. **输出电压范围**：0V - 3.3V（DAC输出范围）
3. **频率范围**：理论上支持DC到400kHz（奈奎斯特频率的一半）
4. **实时性能**：每个采样点的处理延迟约1.2μs
5. **基频检测精度**：约200Hz（815534Hz / 4096点）

## 调试信息

通过串口可以看到以下调试信息：
- "ADC3 Real-time copy mode started"
- "DAC Real-time mode enabled"
- "Detected fundamental frequency: XXX.XX Hz"
- "ADC3 Real-time copy mode stopped"
- "DAC Real-time mode disabled"
