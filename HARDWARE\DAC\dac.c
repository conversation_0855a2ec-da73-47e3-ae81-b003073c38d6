#include "dac.h"

// 定义PI常数（如果编译器没有定义）
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// 频率-幅度映射表 (100Hz-3000Hz，每100Hz一个点，超过3000Hz使用固定幅度)
static const float amplitude_table[DAC_FREQ_TABLE_SIZE] = {
    4.615, 4.462, 4.325, 4.058, 3.727,
    3.448, 3.175, 2.924, 2.686, 2.475,
    2.278, 2.117, 1.977, 1.876, 1.764,
    1.670, 1.574, 1.482, 1.398, 1.324,
    1.255, 1.192, 1.133, 1.073, 1.024,
    0.974, 0.9266, 0.8827, 0.8415, 0.825
};

// 正弦波查找表
static uint16_t sine_table[DAC_SINE_SAMPLES];

// DMA缓冲区 - 用于循环输出正弦波数据
static uint16_t dma_buffer[DAC_SINE_SAMPLES];

// 当前使用的DMA缓冲区大小
static uint32_t current_dma_buffer_size = DAC_SINE_SAMPLES;

// 静态函数前向声明
static void DAC_GenerateSineTable(float amplitude);
static void DAC_UpdateDMABuffer(void);
static void DAC_UpdateTimerFrequency(float sample_rate);

// 正弦波输出控制变量
float dac_output_frequency = 100.0f;    // 当前输出频率
uint8_t dac_output_enabled = 0;         // 输出使能标志（默认关闭）
uint8_t dac_user_enabled = 0;           // 用户使能标志（按钮控制）
float dac_amplitude_multiplier = 1.0f;  // 幅度倍数 (1.0, 1.1, 1.2, ..., 2.0)
static uint32_t sine_index = 0;         // 当前正弦波索引
static float phase_increment = 0.0f;    // 相位增量
static float current_phase = 0.0f;      // 当前相位
static float current_amplitude = 0.5f;  // 当前幅度

/**
 * @brief  初始化DAC通道1 (PA4)
 * @param  None
 * @retval None
 * @note   PA4配置为DAC_OUT1，12位分辨率
 */
void DAC_PA4_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    DAC_InitTypeDef DAC_InitStructure;
    
    // 使能GPIOA和DAC时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_DAC, ENABLE);
    
    // 配置PA4为模拟模式
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;        // 模拟模式
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;   // 无上下拉
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 配置DAC通道1
    DAC_InitStructure.DAC_Trigger = DAC_Trigger_T6_TRGO;                 // TIM6触发
    DAC_InitStructure.DAC_WaveGeneration = DAC_WaveGeneration_None;      // 无波形生成
    DAC_InitStructure.DAC_LFSRUnmask_TriangleAmplitude = DAC_LFSRUnmask_Bit0;
    DAC_InitStructure.DAC_OutputBuffer = DAC_OutputBuffer_Enable;        // 使能输出缓冲器
    
    DAC_Init(DAC_Channel_1, &DAC_InitStructure);
    
    // 使能DAC通道1
    DAC_Cmd(DAC_Channel_1, ENABLE);
    
    // 设置初始输出值为1.65V (中间值)
    DAC_SetChannel1Voltage(DAC_OFFSET_VOLTAGE);
}

/**
 * @brief  初始化DAC的DMA配置
 * @param  None
 * @retval None
 * @note   配置DMA1 Stream5 Channel7用于DAC1输出
 */
void DAC_DMA_Init(void)
{
    DMA_InitTypeDef DMA_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    // 使能DMA1时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA1, ENABLE);

    // 复位DMA1 Stream5
    DMA_DeInit(DMA1_Stream5);
    while (DMA_GetCmdStatus(DMA1_Stream5) != DISABLE) {}

    // 配置DMA1 Stream5
    DMA_InitStructure.DMA_Channel = DMA_Channel_7;                          // DAC1使用Channel7
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&(DAC->DHR12R1);   // DAC1数据寄存器地址
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)dma_buffer;           // DMA缓冲区地址
    DMA_InitStructure.DMA_DIR = DMA_DIR_MemoryToPeripheral;                 // 内存到外设
    DMA_InitStructure.DMA_BufferSize = DAC_SINE_SAMPLES;                    // 缓冲区大小
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;        // 外设地址不递增
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;                 // 内存地址递增
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord; // 16位数据
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;     // 16位数据
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;                         // 循环模式
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;                     // 高优先级
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;                  // 禁用FIFO
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;             // 单次传输
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;     // 单次传输

    DMA_Init(DMA1_Stream5, &DMA_InitStructure);

    // 配置DMA中断（可选，用于调试）
    NVIC_InitStructure.NVIC_IRQChannel = DMA1_Stream5_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0x03;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0x02;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // 使能DMA传输完成中断（可选）
    DMA_ITConfig(DMA1_Stream5, DMA_IT_TC, ENABLE);
}

/**
 * @brief  生成正弦波查找表
 * @param  amplitude: 正弦波幅度
 * @retval None
 * @note   根据指定幅度生成正弦波查找表
 */
static void DAC_GenerateSineTable(float amplitude)
{
    uint32_t i;

    // 生成正弦波查找表
    for (i = 0; i < DAC_SINE_SAMPLES; i++)
    {
        float angle = (2.0f * M_PI * i) / DAC_SINE_SAMPLES;
        float sine_value = sinf(angle);

        // 将正弦波从[-1,1]映射到指定范围
        // 公式: voltage = 1.65V + amplitude * sin(angle)
        float voltage = DAC_OFFSET_VOLTAGE + (sine_value * amplitude);

        // 限制电压范围在0-3.3V之间
        if (voltage < DAC_MIN_VOLTAGE)
            voltage = DAC_MIN_VOLTAGE;
        if (voltage > DAC_MAX_VOLTAGE)
            voltage = DAC_MAX_VOLTAGE;

        // 转换为DAC值 (0-4095)
        sine_table[i] = (uint16_t)((voltage / DAC_VREF) * 4095.0f);
    }
}

/**
 * @brief  初始化DAC正弦波输出
 * @param  None
 * @retval None
 * @note   生成正弦波查找表，配置DMA
 */
void DAC_SineWave_Init(void)
{
    // 初始化DMA
    DAC_DMA_Init();

    // 获取初始频率对应的幅度
    current_amplitude = DAC_GetAmplitudeForFrequency(dac_output_frequency);

    // 生成初始正弦波查找表
    DAC_GenerateSineTable(current_amplitude);

    // 复制查找表到DMA缓冲区
    DAC_UpdateDMABuffer();
}

/**
 * @brief  更新DMA缓冲区
 * @param  None
 * @retval None
 * @note   根据频率智能调整DMA缓冲区大小和内容
 */
static void DAC_UpdateDMABuffer(void)
{
    uint32_t i;

    // 根据频率决定DMA缓冲区的有效大小，目标是保持采样率在800kHz以下
    if (dac_output_frequency <= 3000.0f) {
        // 低频：使用完整的256点，最佳波形质量 (最高768kHz采样率)
        current_dma_buffer_size = DAC_SINE_SAMPLES;
        for (i = 0; i < DAC_SINE_SAMPLES; i++) {
            dma_buffer[i] = sine_table[i];
        }
    } else if (dac_output_frequency <= 6000.0f) {
        // 中频：使用128点，良好波形质量 (最高768kHz采样率)
        current_dma_buffer_size = DAC_SINE_SAMPLES / 2;
        for (i = 0; i < current_dma_buffer_size; i++) {
            // 从256点表中均匀采样128点
            uint32_t table_index = (i * DAC_SINE_SAMPLES) / current_dma_buffer_size;
            dma_buffer[i] = sine_table[table_index];
        }
    } else if (dac_output_frequency <= 12000.0f) {
        // 高频：使用64点，可接受波形质量 (最高768kHz采样率)
        current_dma_buffer_size = DAC_SINE_SAMPLES / 4;
        for (i = 0; i < current_dma_buffer_size; i++) {
            uint32_t table_index = (i * DAC_SINE_SAMPLES) / current_dma_buffer_size;
            dma_buffer[i] = sine_table[table_index];
        }
    } else {
        // 超高频：使用32点，基本波形质量 (最高3.2MHz采样率，但32点足够)
        current_dma_buffer_size = DAC_SINE_SAMPLES / 8;
        for (i = 0; i < current_dma_buffer_size; i++) {
            uint32_t table_index = (i * DAC_SINE_SAMPLES) / current_dma_buffer_size;
            dma_buffer[i] = sine_table[table_index];
        }
    }
}

/**
 * @brief  根据频率获取对应的幅度
 * @param  frequency: 输入频率 (Hz)
 * @retval 对应的幅度值
 * @note   使用线性插值计算中间频率的幅度
 */
float DAC_GetAmplitudeForFrequency(float frequency)
{
    // 频率超过100kHz时返回0
    if (frequency > DAC_MAX_FREQ_HZ)
        return 0.0f;

    // 频率低于100Hz时使用100Hz的幅度
    if (frequency < DAC_BASE_FREQ)
        frequency = DAC_BASE_FREQ;

    // 计算在表中的位置
    float table_index = (frequency - DAC_BASE_FREQ) / DAC_FREQ_STEP;

    // 如果超出表范围，使用1V峰峰值
    if (table_index >= DAC_FREQ_TABLE_SIZE)
        return 0.5f;  // 1V峰峰值对应0.5V幅度

    // 获取整数部分和小数部分
    uint32_t index = (uint32_t)table_index;
    float fraction = table_index - index;

    // 线性插值
    float amplitude_factor;
    if (index >= DAC_FREQ_TABLE_SIZE - 1)
    {
        amplitude_factor = amplitude_table[DAC_FREQ_TABLE_SIZE - 1];
    }
    else
    {
        amplitude_factor = amplitude_table[index] +
                          fraction * (amplitude_table[index + 1] - amplitude_table[index]);
    }

    // 计算实际幅度：DAC峰峰值 = (1V / amplitude_factor) * multiplier
    // 这样经过外部电路放大后：DAC峰峰值 * amplitude_factor = 1V * multiplier
    float peak_to_peak = (1.0f / amplitude_factor) * dac_amplitude_multiplier;
    float amplitude = peak_to_peak / 2.0f;

    // 限制幅度不超过1.65V (确保电压在0-3.3V范围内)
    if (amplitude > DAC_OFFSET_VOLTAGE)
        amplitude = DAC_OFFSET_VOLTAGE;

    return amplitude;
}

/**
 * @brief  设置DAC通道1的数字值
 * @param  value: 12位数字值 (0-4095)
 * @retval None
 * @note   输出电压 = (value / 4095) * VREF+
 */
void DAC_SetChannel1Value(uint16_t value)
{
    // 限制值在12位范围内
    if (value > 4095)
        value = 4095;
    
    DAC_SetChannel1Data(DAC_Align_12b_R, value);
}

/**
 * @brief  设置DAC通道1的输出电压
 * @param  voltage: 输出电压值 (0.0V - 3.3V)
 * @retval None
 * @note   假设VREF+ = 3.3V
 */
void DAC_SetChannel1Voltage(float voltage)
{
    uint16_t dac_value;
    
    // 限制电压范围
    if (voltage < 0.0f)
        voltage = 0.0f;
    if (voltage > 3.3f)
        voltage = 3.3f;
    
    // 计算对应的DAC值
    dac_value = (uint16_t)((voltage / 3.3f) * 4095.0f);
    
    DAC_SetChannel1Value(dac_value);
}

/**
 * @brief  设置DAC正弦波输出频率
 * @param  frequency: 输出频率 (Hz)
 * @retval None
 * @note   当频率超过100kHz时，停止输出；频率改变时重新生成查找表
 */
void DAC_SetSineFrequency(float frequency)
{
    dac_output_frequency = frequency;

    // 检查频率限制
    if (frequency > DAC_MAX_FREQ_HZ)
    {
        DAC_StopSineOutput();
        return;
    }

    // 获取新频率对应的幅度
    float new_amplitude = DAC_GetAmplitudeForFrequency(frequency);

    // 如果幅度发生变化，重新生成查找表和DMA缓冲区
    if (new_amplitude != current_amplitude)
    {
        current_amplitude = new_amplitude;
        DAC_GenerateSineTable(current_amplitude);
        DAC_UpdateDMABuffer();
    }

    // 计算所需的定时器频率
    // 根据当前DMA缓冲区大小计算采样率
    float required_sample_rate = frequency * current_dma_buffer_size;

    // 更新TIM6的频率以匹配所需的采样率
    DAC_UpdateTimerFrequency(required_sample_rate);

    // 如果用户使能且频率在允许范围内，启动输出
    if (dac_user_enabled && frequency > 0 && frequency <= DAC_MAX_FREQ_HZ)
    {
        DAC_StartSineOutput();
    }
    else
    {
        DAC_StopSineOutput();
    }
}

/**
 * @brief  更新定时器频率
 * @param  sample_rate: 所需的采样率 (Hz)
 * @retval None
 * @note   动态调整TIM6的频率以匹配所需的采样率
 */
static void DAC_UpdateTimerFrequency(float sample_rate)
{
    // 限制采样率在合理范围内
    if (sample_rate < 1000.0f) sample_rate = 1000.0f;        // 最小1kHz
    if (sample_rate > 42000000.0f) sample_rate = 42000000.0f; // 最大42MHz (84MHz/2)

    // 计算定时器参数
    // TIM6时钟频率：84MHz
    uint32_t timer_freq = 84000000;
    uint32_t period = (uint32_t)(timer_freq / sample_rate);

    // 确保period在有效范围内 (最小值为2，最大值为65535)
    if (period < 2) period = 2;
    if (period > 65535) period = 65535;

    // 停止定时器
    TIM_Cmd(TIM6, DISABLE);

    // 等待定时器停止
    while(TIM_GetFlagStatus(TIM6, TIM_FLAG_Update) == SET)
    {
        TIM_ClearFlag(TIM6, TIM_FLAG_Update);
    }

    // 更新定时器周期
    TIM6->ARR = period - 1;

    // 重置计数器
    TIM6->CNT = 0;

    // 重新启动定时器
    TIM_Cmd(TIM6, ENABLE);

    // 调试信息：打印实际配置的参数
    #ifdef DEBUG_DAC_TIMER
    printf("DAC Timer: freq=%.0f, period=%d, actual_freq=%d\r\n",
           sample_rate, period, timer_freq/period);
    #endif
}

/**
 * @brief  启动DAC正弦波输出
 * @param  None
 * @retval None
 */
void DAC_StartSineOutput(void)
{
    if (dac_user_enabled && dac_output_frequency <= DAC_MAX_FREQ_HZ)
    {
        dac_output_enabled = 1;

        // 停止DMA
        DMA_Cmd(DMA1_Stream5, DISABLE);

        // 重新设置DMA缓冲区大小
        DMA1_Stream5->NDTR = current_dma_buffer_size;

        // 启动DMA
        DMA_Cmd(DMA1_Stream5, ENABLE);

        // 使能DAC的DMA请求
        DAC_DMACmd(DAC_Channel_1, ENABLE);
    }
}

/**
 * @brief  停止DAC正弦波输出
 * @param  None
 * @retval None
 */
void DAC_StopSineOutput(void)
{
    dac_output_enabled = 0;

    // 禁用DAC的DMA请求
    DAC_DMACmd(DAC_Channel_1, DISABLE);

    // 停止DMA
    DMA_Cmd(DMA1_Stream5, DISABLE);

    // 输出中间电压1.65V
    DAC_SetChannel1Voltage(DAC_OFFSET_VOLTAGE);
}

/**
 * @brief  更新DAC正弦波输出 (已改为DMA模式，不再使用此函数)
 * @param  None
 * @retval None
 * @note   DMA模式下，硬件自动从缓冲区读取数据输出
 */
/*
void DAC_UpdateSineOutput(void)
{
    if (!dac_output_enabled)
        return;

    // 更新相位
    current_phase += phase_increment;

    // 相位回绕
    if (current_phase >= DAC_SINE_SAMPLES)
    {
        current_phase -= DAC_SINE_SAMPLES;
    }

    // 获取当前索引
    sine_index = (uint32_t)current_phase;

    // 输出正弦波值
    DAC_SetChannel1Value(sine_table[sine_index]);
}
*/

/**
 * @brief  设置DAC幅度倍数
 * @param  multiplier: 幅度倍数 (1.0-2.0)
 * @retval None
 */
void DAC_SetAmplitudeMultiplier(float multiplier)
{
//    // 限制倍数范围在1.0-2.0之间
//    if (multiplier < 1.0f)
//        multiplier = 1.0f;
//    if (multiplier > 2.0f)
//        multiplier = 2.0f;

    dac_amplitude_multiplier = multiplier;

    // 重新设置当前频率以更新查找表
    DAC_SetSineFrequency(dac_output_frequency);
}

/**
 * @brief  切换到下一个幅度倍数
 * @param  None
 * @retval None
 * @note   循环：1.0 -> 1.1 -> 1.2 -> ... -> 2.0 -> 1.0
 */
void DAC_NextAmplitudeMultiplier(void)
{
    // 增加0.1倍数
    dac_amplitude_multiplier += 0.1f;

    // 如果超过2.0，回到1.0
    if (dac_amplitude_multiplier > 2.05f)
        dac_amplitude_multiplier = 1.0f;

    // 重新设置当前频率以更新查找表
    DAC_SetSineFrequency(dac_output_frequency);
}

/**
 * @brief  获取当前DAC幅度倍数
 * @param  None
 * @retval 当前幅度倍数
 */
float DAC_GetAmplitudeMultiplier(void)
{
    return dac_amplitude_multiplier;
}

/**
 * @brief  设置DAC用户使能状态
 * @param  enable: 1-使能, 0-禁用
 * @retval None
 */
void DAC_SetUserEnable(uint8_t enable)
{
    dac_user_enabled = enable;

    if (enable)
    {
        // 用户使能时，重新设置当前频率以确保正确的幅度和相位增量
        DAC_SetSineFrequency(dac_output_frequency);
    }
    else
    {
        // 用户禁用时，停止输出
        DAC_StopSineOutput();
    }
}

/**
 * @brief  获取DAC用户使能状态
 * @param  None
 * @retval 用户使能状态
 */
uint8_t DAC_GetUserEnable(void)
{
    return dac_user_enabled;
}

/**
 * @brief  使能DAC通道1
 * @param  None
 * @retval None
 */
void DAC_Enable(void)
{
    DAC_Cmd(DAC_Channel_1, ENABLE);
}

/**
 * @brief  失能DAC通道1
 * @param  None
 * @retval None
 */
void DAC_Disable(void)
{
    DAC_Cmd(DAC_Channel_1, DISABLE);
}

/**
 * @brief  DAC测试函数 - 输出不同电压值进行测试
 * @param  None
 * @retval None
 * @note   可以用万用表测量PA4引脚的电压变化
 */
void DAC_Test(void)
{
    // 测试不同的电压输出
    DAC_SetChannel1Voltage(0.0f);   // 0V
    delay_ms(1000);

    DAC_SetChannel1Voltage(1.65f);  // 1.65V (中间值)
    delay_ms(1000);

    DAC_SetChannel1Voltage(3.3f);   // 3.3V (最大值)
    delay_ms(1000);

    DAC_SetChannel1Voltage(0.8f);   // 0.8V
    delay_ms(1000);

    DAC_SetChannel1Voltage(2.5f);   // 2.5V
    delay_ms(1000);

    // 回到中间值
    DAC_SetChannel1Voltage(DAC_OFFSET_VOLTAGE);
}

/**
 * @brief  DMA1 Stream5中断处理函数
 * @param  None
 * @retval None
 * @note   用于调试DMA传输状态（可选）
 */
void DMA1_Stream5_IRQHandler(void)
{
    if (DMA_GetITStatus(DMA1_Stream5, DMA_IT_TCIF5) == SET)
    {
        // DMA传输完成中断
        // 在循环模式下，这个中断表示一个完整的正弦波周期已经输出完成

        // 清除中断标志位
        DMA_ClearITPendingBit(DMA1_Stream5, DMA_IT_TCIF5);
    }
}

/**
 * @brief  DAC简单测试函数 - 输出固定电压
 * @param  None
 * @retval None
 * @note   用于验证DAC基本功能
 */
void DAC_SimpleTest(void)
{
    // 输出2.0V测试电压
    DAC_SetChannel1Voltage(2.0f);
}

// DAC实时模式相关变量
static uint8_t dac_realtime_mode = 0;

// 实时模式滤波相关变量
#define DAC_FILTER_SIZE_MAX 8       // 最大滤波器大小
static uint16_t filter_buffer[DAC_FILTER_SIZE_MAX];
static uint8_t filter_index = 0;
static uint8_t current_filter_size = 4;  // 当前滤波器大小
static uint16_t last_adc_value = 2048;   // 上一个ADC值，初始化为中点
static uint8_t filter_level = 1;        // 滤波强度等级 (0-3)

/**
 * @brief  初始化实时模式滤波器
 * @param  None
 * @retval None
 */
static void DAC_InitRealtimeFilter(void)
{
    // 初始化滤波器缓冲区为中点值
    for (int i = 0; i < DAC_FILTER_SIZE_MAX; i++) {
        filter_buffer[i] = 2048;  // 12位ADC的中点值
    }
    filter_index = 0;
    last_adc_value = 2048;

    // 根据滤波等级设置滤波器大小
    DAC_SetRealtimeFilterLevel(filter_level);
}

/**
 * @brief  设置实时模式滤波强度
 * @param  level: 滤波强度等级 (0-3)
 *         0: 无滤波
 *         1: 轻度滤波 (2点平均)
 *         2: 中度滤波 (4点平均)
 *         3: 重度滤波 (8点平均)
 * @retval None
 */
void DAC_SetRealtimeFilterLevel(uint8_t level)
{
    if (level > 3) level = 3;

    filter_level = level;

    switch (level) {
        case 0:
            current_filter_size = 1;  // 无滤波
            break;
        case 1:
            current_filter_size = 2;  // 轻度滤波
            break;
        case 2:
            current_filter_size = 4;  // 中度滤波
            break;
        case 3:
            current_filter_size = 8;  // 重度滤波
            break;
    }

    printf("DAC filter level set to %d (size: %d)\r\n", level, current_filter_size);
}

/**
 * @brief  设置DAC实时模式
 * @param  enable: 1-启用实时模式，0-禁用实时模式
 * @retval None
 * @note   实时模式下DAC直接输出ADC采样值，不使用正弦波查找表
 */
void DAC_SetRealtimeMode(uint8_t enable)
{
    dac_realtime_mode = enable;

    if (enable) {
        // 启用实时模式
        // 初始化滤波器
        DAC_InitRealtimeFilter();

        // 停止正弦波DMA输出
        DAC_StopSineOutput();

        // 禁用DAC的DMA请求
        DAC_DMACmd(DAC_Channel_1, DISABLE);

        // 停止DMA
        DMA_Cmd(DMA1_Stream5, DISABLE);

        printf("DAC Real-time mode enabled with filtering\r\n");
    } else {
        // 禁用实时模式
        // 恢复正弦波模式（如果用户使能）
        if (dac_user_enabled) {
            DAC_SetSineFrequency(dac_output_frequency);
        }

        printf("DAC Real-time mode disabled\r\n");
    }
}

/**
 * @brief  获取DAC实时模式状态
 * @param  None
 * @retval 实时模式状态：1-启用，0-禁用
 */
uint8_t DAC_GetRealtimeMode(void)
{
    return dac_realtime_mode;
}

/**
 * @brief  移动平均滤波器（可配置强度）
 * @param  new_value: 新的ADC值
 * @retval 滤波后的值
 */
static uint16_t DAC_MovingAverageFilter(uint16_t new_value)
{
    if (filter_level == 0) {
        // 无滤波，直接返回原值
        return new_value;
    }

    // 将新值添加到滤波缓冲区
    filter_buffer[filter_index] = new_value;
    filter_index = (filter_index + 1) % current_filter_size;

    // 计算平均值
    uint32_t sum = 0;
    for (int i = 0; i < current_filter_size; i++) {
        sum += filter_buffer[i];
    }

    return (uint16_t)(sum / current_filter_size);
}

/**
 * @brief  线性插值函数
 * @param  start_value: 起始值
 * @param  end_value: 结束值
 * @param  step: 当前步骤 (0 到 factor-1)
 * @param  factor: 插值倍数
 * @retval 插值结果
 */
static uint16_t DAC_LinearInterpolate(uint16_t start_value, uint16_t end_value, uint8_t step, uint8_t factor)
{
    if (factor == 0) return end_value;

    int32_t diff = (int32_t)end_value - (int32_t)start_value;
    int32_t interpolated = start_value + (diff * step) / factor;

    // 限制在12位范围内
    if (interpolated < 0) interpolated = 0;
    if (interpolated > 4095) interpolated = 4095;

    return (uint16_t)interpolated;
}

/**
 * @brief  DAC实时输出ADC采样值（带滤波，简化版）
 * @param  adc_value: ADC采样值 (0-4095)
 * @retval None
 * @note   使用移动平均滤波改善波形质量，避免在中断中进行复杂处理
 */
void DAC_RealtimeOutput(uint16_t adc_value)
{
    if (dac_realtime_mode && dac_user_enabled) {
        // 限制值在12位范围内
        if (adc_value > 4095)
            adc_value = 4095;

        // 应用移动平均滤波
        uint16_t filtered_value = DAC_MovingAverageFilter(adc_value);

        // 简单的线性插值：在当前值和上一个值之间取平均
        uint16_t interpolated_value = (last_adc_value + filtered_value) / 2;

        // 输出插值后的值
        DAC_SetChannel1Data(DAC_Align_12b_R, interpolated_value);

        // 保存当前值作为下次插值的起始点
        last_adc_value = filtered_value;
    }
}
